import { Event } from "@/database";
import connectDB from "@/lib/mongodb";
import { NextRequest, NextResponse } from "next/server";

export const POST = async (req: NextRequest) => {
  try {
    await connectDB();

    const contentType = req.headers.get("content-type") || "";
    let event;

    try {
      if (contentType.includes("application/json")) {
        // Handle JSON data
        event = await req.json();
      } else if (
        contentType.includes("multipart/form-data") ||
        contentType.includes("application/x-www-form-urlencoded")
      ) {
        // Handle form data
        const formData = await req.formData();
        event = Object.fromEntries(formData.entries());
      } else {
        return NextResponse.json(
          {
            message:
              "Content-Type must be application/json, multipart/form-data, or application/x-www-form-urlencoded",
          },
          { status: 400 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        {
          message: "Invalid event data",
        },
        { status: 400 }
      );
    }
    const newEvent = await Event.create(event);

    return NextResponse.json(
      {
        message: "Event created successfully",
        event: newEvent,
      },
      { status: 201 }
    );
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      {
        message: "Event creation failed:",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
};
