import { EventCard } from "@/components/event-card";
import { ExploreBtn } from "@/components/explore-btn";
import { time } from "console";

const events = [
  {
    image: "/images/event1.png",
    title: "Event 1",
    slug: "event-1",
    location: "New York, USA",
    date: "2024-10-10",
    time: "10:00 AM",
  },
  { image: "/images/event2.png", title: "Event 2" },
];

const Home = () => {
  return (
    <section>
      <h1 className="text-center">
        The Hub for Every Dev <br /> Event You Must Not Miss!
      </h1>
      <p className="text-center mt-5">
        Hackathons, Meetups and Conferences , All in one place
      </p>
      <ExploreBtn />
      <div className="mt-20 space-y-7">
        <h3>Feature Events</h3>
        <ul className="events">
          {events.map((event, index) => (
            <li key={index}>
              <EventCard {...event} />
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Home;
