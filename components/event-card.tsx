import Image from "next/image";
import Link from "next/link";

interface EventCardProps {
  title: string;
  image: string;
}

export const EventCard = ({ title, image }: EventCardProps) => {
  return (
    <Link href="/event" id="event-card">
      <Image
        src={image}
        alt="logo"
        width={410}
        height={300}
        className="poster"
      />
      <p className="title">{title}</p>
    </Link>
  );
};
