{"name": "next-js-16", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"babel-plugin-react-compiler": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.548.0", "next": "16.0.0", "ogl": "^1.0.11", "react": "19.2.0", "react-dom": "19.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "16.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.4.0", "typescript": "^5"}}